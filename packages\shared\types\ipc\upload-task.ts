import { UploadTask } from '../database.types.js'
import { CrudableIpcClient } from '../crudable-ipc-client.js'

/**
 * 上传任务 IPC 客户端接口
 */
export interface UploadTaskIPCClient extends CrudableIpcClient<
  UploadTask.IUploadTask,
  UploadTask.CreateParams,
  UploadTask.QueryParams,
  UploadTask.UpdateParams,
  UploadTask.StatsResult
> {

  /**
   * 获取用户上传任务
   * @param data 参数对象
   * @returns 任务列表
   */
  userTasks(data: { uid: string, status?: UploadTask.Status, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>

  /**
   * 获取文件夹下的上传任务
   * @param data 参数对象
   * @returns 任务列表
   */
  folderTasks(data: { folderId: string, uid: string, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>

  /**
   * 搜索上传任务
   * @param data 参数对象
   * @returns 任务列表
   */
  search(data: { keyword: string, uid: string, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>

  /**
   * 获取上传任务统计
   * @param data 参数对象
   * @returns 统计结果
   */
  getStats(data: { uid: string, teamId?: number | null }): Promise<UploadTask.StatsResult>

  /**
   * 开始上传任务
   * @param data 参数对象
   * @returns 是否成功开始
   */
  startUpload(data: { id: number }): Promise<boolean>

  /**
   * 暂停上传任务
   * @param data 参数对象
   * @returns 是否成功暂停
   */
  pauseUpload(data: { id: number }): Promise<boolean>

  /**
   * 恢复上传任务
   * @param data 参数对象
   * @returns 是否成功恢复
   */
  resumeUpload(data: { id: number }): Promise<boolean>

  /**
   * 取消上传任务
   * @param data 参数对象
   * @returns 是否成功取消
   */
  cancelUpload(data: { id: number }): Promise<boolean>

  /**
   * 重试上传任务
   * @param data 参数对象
   * @returns 是否成功重试
   */
  retryUpload(data: { id: number }): Promise<boolean>

  /**
   * 批量操作上传任务
   * @param data 批量操作参数
   * @returns 操作的任务数量
   */
  batchOperation(data: UploadTask.BatchParams): Promise<number>

  /**
   * 获取上传队列配置
   * @returns 队列配置
   */
  getQueueConfig(): Promise<UploadTask.QueueConfig>

  /**
   * 更新上传队列配置
   * @param config 新的配置
   * @returns 是否更新成功
   */
  updateQueueConfig(config: Partial<UploadTask.QueueConfig>): Promise<boolean>

  /**
   * 获取当前上传队列状态
   * @returns 队列状态信息
   */
  getQueueStatus(): Promise<{
    active_count: number
    pending_count: number
    paused_count: number
    max_concurrent: number
  }>

  /**
   * 清理已完成的任务
   * @param data 参数对象
   * @returns 清理的任务数量
   */
  cleanupCompleted(data: { uid: string, teamId?: number | null, olderThanDays?: number }): Promise<number>

  /**
   * 上传文件内容
   * @param data 参数对象
   * @returns 上传结果
   */
  uploadFileContent(data: {
    taskId: number
    fileContent: ArrayBuffer | Uint8Array
    fileName: string
  }): Promise<{ success: boolean, url?: string, error?: string }>

  /**
   * 选择文件
   * @param data 参数对象
   * @returns 选择的文件路径列表
   */
  selectFiles(data?: {
    multiple?: boolean
    filters?: Array<{ name: string, extensions: string[] }>
  }): Promise<string[]>

  /**
   * 从本地路径上传文件
   * @param data 参数对象
   * @returns 上传结果
   */
  uploadFromPath(data: {
    taskId: number
    filePath: string
  }): Promise<{ success: boolean, url?: string, error?: string }>
}
