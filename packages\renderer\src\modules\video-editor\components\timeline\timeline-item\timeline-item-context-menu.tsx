import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger
} from '@/components/ui/context-menu'
import {
  Copy,
  Crop,
  Edit3,
  Eraser,
  FlipHorizontal,
  Move,
  Plus,
  PlusSquare,
  Save,
  Scissors,
  Search,
  SplitSquareHorizontal,
  Subtitles,
  Trash2,
  Volume2,
  VolumeOff,
  VolumeX,
  Zap
} from 'lucide-react'
import { Overlay, OverlayType, SoundOverlay, VideoOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import { useStoryboardHelper } from '@/modules/video-editor/hooks/helpers/useStoryboardHelper'
import { findOverlaysAboveStorybook, findOverlayStoryboard } from '@/modules/video-editor/utils/overlay-helper'
import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'
import { <PERSON>alog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useTimelineTrackContext } from '@/modules/video-editor/components/timeline/timeline-track/timeline-track-context'
import { useOperationPanelStore } from '@/modules/video-editor/stores/useOperationPanelStore'
import { TrackType } from '@/modules/video-editor/types'
import { useNarrationTrackHelper } from '@/modules/video-editor/hooks/helpers/useNarrationTrackHelper'
import { useVideoHelper } from '@/modules/video-editor/hooks/helpers/useVideoHelper'
import { VideoSliceDialog } from './video-slice-dialog'
import { VideoCropDialog } from '@/components/video-crop-dialog'

/**
 * 菜单项配置接口
 */
interface MenuItemConfig {
  /** 菜单显示名称 */
  label: string

  /** 是否禁用 */
  disabled?: boolean

  /** 适用的 Overlay 类型, 当为 `undefined` 时, 说明适用于所有类型 */
  availableTypes?: OverlayType[]

  /** 快捷键（可选） */
  shortcut?: string

  /** 菜单图标组件 */
  icon?: React.ComponentType<{ className?: string }>

  /** 功能实现的回调函数 */
  onClick: () => void

  /** 二级菜单项（可选） */
  children?: MenuItemConfig[]
}

/**
 * Props for the TimelineItemContextMenu component
 */
interface TimelineItemContextMenuProps {
  /** The content to wrap with the context menu */
  children: React.ReactNode
  /** 完整的 overlay 对象 */
  overlay: Overlay
}

interface BatchAddStoryboardDialogProps {
  /** 是否打开对话框 */
  open: boolean
  /** 关闭对话框的回调 */
  onOpenChange: (open: boolean) => void
  /** 当前选中的分镜overlay */
  overlay: Overlay
  /** 批量新增分镜的回调函数 */
  onBatchAdd: (overlay: Overlay, amount: number) => void
}

/**
 * 批量新增分镜的 Dialog 组件
 *
 * 提供轻量化的输入界面，让用户输入要新增的分镜数量
 *
 * @example
 * ```tsx
 * <BatchAddStoryboardDialog
 *   open={dialogOpen}
 *   onOpenChange={setDialogOpen}
 *   overlay={overlay}
 *   onBatchAdd={storyboardHelper.pushStoryboardAtRightOf}
 * />
 * ```
 */
function BatchAddStoryboardDialog({
  open,
  onOpenChange,
  overlay,
  onBatchAdd,
}: BatchAddStoryboardDialogProps) {
  const [amount, setAmount] = useState<string>('1')
  const [error, setError] = useState<string>('')
  const inputRef = useRef<HTMLInputElement>(null)

  // 当Dialog打开时，自动聚焦到输入框并选中内容
  useEffect(() => {
    if (open && inputRef.current) {
      // 使用setTimeout确保DOM已渲染
      setTimeout(() => {
        inputRef.current?.focus()
        inputRef.current?.select()
      }, 100)
    }
  }, [open])

  // 验证输入值
  const validateAmount = (value: string): boolean => {
    const num = parseInt(value, 10)
    if (isNaN(num) || num < 1 || num > 100) {
      setError('请输入1-100之间的整数')
      return false
    }
    setError('')
    return true
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setAmount(value)

    // 实时验证
    if (value.trim()) {
      validateAmount(value)
    } else {
      setError('')
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleConfirm()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  // 处理确认操作
  const handleConfirm = () => {
    if (!amount.trim()) {
      setError('请输入分镜数量')
      return
    }

    if (!validateAmount(amount)) {
      return
    }

    const numAmount = parseInt(amount, 10)
    onBatchAdd(overlay, numAmount)

    // 重置状态并关闭Dialog
    setAmount('1')
    setError('')
    onOpenChange(false)
  }

  // 处理取消操作
  const handleCancel = () => {
    setAmount('1')
    setError('')
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>批量新增分镜</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="amount-input" className="text-sm font-medium">
              请输入要新增的分镜数量：
            </label>
            <Input
              id="amount-input"
              ref={inputRef}
              type="number"
              min="1"
              max="100"
              value={amount}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="输入1-100之间的数字"
              className={error ? 'border-red-500 focus-visible:ring-red-500' : ''}
            />
            {error && (
              <p className="text-sm text-red-500">{error}</p>
            )}
          </div>

          <div className="text-sm text-muted-foreground">
            将在当前分镜右侧新增 {amount || '0'} 个分镜
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleConfirm} disabled={!!error || !amount.trim()}>
            确认新增
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * 渲染菜单项组件
 */
const MenuItemRenderer: React.FC<{
  item: MenuItemConfig
  overlay: Overlay
}> = ({ item, overlay }) => {
  const IconComponent = item.icon

  // 如果有子菜单，渲染为子菜单触发器
  if (item.children && item.children.length > 0) {
    return (
      <ContextMenuSub>
        <ContextMenuSubTrigger
          disabled={item.disabled}
          className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
        >
          {IconComponent && <IconComponent className="mr-4 h-4 w-4" />}
          {item.label}
        </ContextMenuSubTrigger>
        <ContextMenuSubContent className="dark:bg-slate-900 dark:border-slate-800">
          {item.children
            .filter(child => !child.availableTypes || child.availableTypes.includes(overlay.type))
            .map((child, index) => (
              <MenuItemRenderer key={index} item={child} overlay={overlay} />
            ))}
        </ContextMenuSubContent>
      </ContextMenuSub>
    )
  }

  // 普通菜单项
  return (
    <ContextMenuItem
      disabled={item.disabled}
      className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
      onClick={e => {
        e.stopPropagation()
        item.onClick()
      }}
    >
      {IconComponent && <IconComponent className="mr-4 h-4 w-4" />}
      <span className="flex-1">{item.label}</span>
      {item.shortcut && (
        <span className="ml-auto text-xs text-muted-foreground">
          {item.shortcut}
        </span>
      )}
    </ContextMenuItem>
  )
}

/**
 * 基于 Overlay 类型的动态上下文菜单组件
 * 根据传入的 overlay 的 type 来动态显示对应的菜单项
 *
 * @example
 * ```tsx
 * <TimelineItemContextMenu overlay={videoOverlay}>
 *   <TimelineItem />
 * </TimelineItemContextMenu>
 * ```
 */
export const TimelineItemContextMenu: React.FC<TimelineItemContextMenuProps> = ({
  children,
  overlay,
}) => {
  const { clipboard, setIsContextMenuOpen } = useTimeline()
  const { tracks, deleteOverlay, updateOverlay } = useEditorContext()
  const { currentTrack } = useTimelineTrackContext()

  const { openTextToSpeechPanel } = useOperationPanelStore()

  const overlayHelper = useOverlayHelper()
  const videoHelper = useVideoHelper()
  const narrationTrackHelper = useNarrationTrackHelper()
  const storyboardHelper = useStoryboardHelper()

  // 批量新增分镜对话框状态
  const [batchAddDialogOpen, setBatchAddDialogOpen] = useState(false)

  // 视频切片对话框状态
  const [videoSliceDialogOpen, setVideoSliceDialogOpen] = useState(false)

  const [videoCropDialogOpen, setVideoCropDialogOpen] = useState(false)

  // 创建菜单配置
  const rawMenuItems = useMemo(
    (): MenuItemConfig[] => {
      const baseItems: MenuItemConfig[] = [
        {
          label: '复制',
          shortcut: 'Ctrl+C',
          icon: Copy,
          onClick: () => clipboard.copyOverlay(overlay),
        },
        {
          label: '剪切',
          shortcut: 'Ctrl+X',
          icon: Scissors,
          onClick: () => clipboard.clipOverlay(overlay)
        },
        {
          label: '删除',
          shortcut: 'Delete',
          icon: Trash2,
          onClick: () => {
            // 清理视频类型的关键帧缓存
            if (overlay.type === OverlayType.VIDEO) {
              void cacheManager.keyframe.clearKeyframes(overlay.src)
            }
            deleteOverlay(overlay.id)
          }
        },
      ]

      if (overlay.type === OverlayType.STORYBOARD) {
        const aboveOverlays = findOverlaysAboveStorybook(tracks, overlay)
        const allVideoMuted = aboveOverlays
          .filter(o => o.type === OverlayType.VIDEO)
          .every(o => o.styles.volume === 0)

        return [
          {
            label: '向右新增分镜',
            availableTypes: [OverlayType.STORYBOARD],
            icon: Plus,
            onClick: () => storyboardHelper.pushStoryboardAtRightOf(overlay)
          },
          {
            label: '向右批量新增分镜',
            availableTypes: [OverlayType.STORYBOARD],
            icon: PlusSquare,
            onClick: () => setBatchAddDialogOpen(true)
          },
          {
            label: allVideoMuted ? '开启分镜视频声音' : '关闭分镜视频声音',
            availableTypes: [OverlayType.STORYBOARD],
            icon: VolumeX,
            onClick: () => storyboardHelper.setVolumeForVideosInStoryboard(overlay, allVideoMuted ? 1 : 0)
          },
          {
            label: '一键智能变速',
            availableTypes: [OverlayType.STORYBOARD],
            icon: Zap,
            onClick: () => storyboardHelper.adjustDurationOfVideosInStoryboard(overlay)
          },
          {
            label: '保存分镜素材至素材库',
            availableTypes: [OverlayType.STORYBOARD],
            icon: Save,
            onClick: () => storyboardHelper.saveMaterialToLibrary(overlay)
          },
          {
            label: '清空分镜素材',
            availableTypes: [OverlayType.STORYBOARD],
            icon: Eraser,
            onClick: () => storyboardHelper.clearOverlaysInStoryboard(overlay)
          },
          ...baseItems,
        ]
      }

      if (currentTrack.type === TrackType.NARRATION) {
        if (overlay.type === OverlayType.SOUND) {
          baseItems.unshift({
            label: '识别字幕',
            icon: Subtitles,
            onClick: () => {
              return narrationTrackHelper.recognizeSoundOverlayAsText(overlay as SoundOverlay, currentTrack)
            }
          })
        }

        if (overlay.type === OverlayType.TEXT) {
          baseItems.unshift({
            label: '生成配音',
            icon: Volume2,
            onClick: () => {
              return openTextToSpeechPanel({ ...overlay, currentTrack })
            }
          })
        }
      }

      if (overlay.type === OverlayType.SOUND || overlay.type === OverlayType.VIDEO) {
        baseItems.unshift({
          label: overlay.styles.volume ? '静音' : '取消静音',
          icon: overlay.styles.volume ? Volume2 : VolumeOff,
          onClick() {
            return updateOverlay(overlay.id, {
              styles: {
                volume: overlay.styles.volume ? 0 : 100
              }
            })
          }
        })
      }

      if (overlay.type === OverlayType.VIDEO) {
        const originalStoryboard = findOverlayStoryboard(tracks, overlay)
        const storyboards = tracks
          .find(o => o.type === TrackType.STORYBOARD)
          ?.overlays || []

        baseItems.unshift(
          {
            label: '裁剪',
            availableTypes: [OverlayType.VIDEO],
            icon: Crop,
            onClick: () => setVideoCropDialogOpen(true)
          },
          {
            label: '基础编辑',
            availableTypes: [OverlayType.VIDEO],
            icon: Edit3,
            onClick: () => void 0, // 父级菜单不需要点击事件
            children: [
              {
                label: '镜像',
                availableTypes: [OverlayType.VIDEO],
                icon: FlipHorizontal,
                onClick: () => videoHelper.flipCurrentOverlay(overlay)
              },
              {
                label: '镜像并复制',
                availableTypes: [OverlayType.VIDEO],
                icon: FlipHorizontal,
                onClick: () => videoHelper.flipCurrentOverlay(overlay, true)
              },
              {
                label: '智能变速',
                availableTypes: [OverlayType.VIDEO],
                icon: Zap,
                onClick: () => videoHelper.autoAdjustVideoSpeed(overlay)
              },
              {
                label: '切片',
                availableTypes: [OverlayType.VIDEO],
                icon: SplitSquareHorizontal,
                onClick: () => setVideoSliceDialogOpen(true)
              }
            ]
          },
          {
            label: '移动到',
            icon: Move,
            onClick: () => void 0,
            children: storyboards.map((_, index) => ({
              label: '分镜' + (index + 1),
              disabled: index === originalStoryboard?.index,
              onClick: () => overlayHelper.moveOverlayToStoryboardIndexAt(overlay, index),
            }))
          },
          {
            label: '查找原素材',
            availableTypes: [OverlayType.VIDEO],
            icon: Search,
            onClick: () => {
              // TODO: 实现查找原素材功能
            }
          },
          {
            label: '保存至素材库',
            availableTypes: [OverlayType.VIDEO],
            icon: Save,
            onClick: () => {
              // TODO: 实现保存至素材库功能
            }
          },
        )
      }

      return baseItems
    },
    [overlay, overlayHelper, videoHelper, narrationTrackHelper, storyboardHelper, tracks, clipboard, deleteOverlay]
  )

  // 根据当前 overlay 类型过滤可用的菜单项
  const availableMenuItems = useMemo(
    () => rawMenuItems.filter(item =>
      !item.availableTypes || item.availableTypes.includes(overlay.type)
    ),
    [rawMenuItems, overlay.type]
  )

  return (
    <>
      <ContextMenu onOpenChange={setIsContextMenuOpen}>
        <ContextMenuTrigger className="z-[100]">{children}</ContextMenuTrigger>
        <ContextMenuContent className="dark:bg-slate-900 dark:border-slate-800">
          {availableMenuItems.map((item, index) => (
            <MenuItemRenderer key={index} item={item} overlay={overlay} />
          ))}
        </ContextMenuContent>
      </ContextMenu>

      {/* 批量新增分镜对话框 */}
      <BatchAddStoryboardDialog
        open={batchAddDialogOpen}
        onOpenChange={setBatchAddDialogOpen}
        overlay={overlay}
        onBatchAdd={storyboardHelper.pushStoryboardAtRightOf}
      />

      {/* 视频切片对话框 */}
      {overlay.type === OverlayType.VIDEO && (
        <VideoSliceDialog
          open={videoSliceDialogOpen}
          onOpenChange={setVideoSliceDialogOpen}
          overlay={overlay as VideoOverlay}
          onConfirm={sliceDurationInFrames => {
            videoHelper.autoSliceVideo(overlay as VideoOverlay, sliceDurationInFrames)
          }}
        />
      )}

      {/* 视频裁剪对话框 */}
      {overlay.type === OverlayType.VIDEO && (
        <VideoCropDialog
          open={videoCropDialogOpen}
          onOpenChange={setVideoCropDialogOpen}
          overlay={overlay as VideoOverlay}
        />
      )}
    </>
  )
}
