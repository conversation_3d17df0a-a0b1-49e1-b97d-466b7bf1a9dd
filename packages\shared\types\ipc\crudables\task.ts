import { UploadTask } from '../../database.types.js'
import { CrudableIpcClient } from '../../crudable-ipc-client.js'

/**
 * 任务IPC客户端接口
 */
export interface TaskIPCClient extends CrudableIpcClient<
  UploadTask.IUploadTask,
  UploadTask.CreateParams,
  UploadTask.QueryParams,
  UploadTask.UpdateParams,
  UploadTask.StatsResult
> {
  /**
   * 获取用户任务
   * @param data 参数对象
   * @returns 任务列表
   */
  userTasks(data: { uid: string, status?: number, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>;

  /**
   * 获取文件夹下的任务
   * @param data 参数对象
   * @returns 任务列表
   */
  folderTasks(data: { folderId: string, uid: string, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>;

  /**
   * 根据类型获取任务
   * @param data 参数对象
   * @returns 任务列表
   */
  tasksByType(data: { uid: string, type: number, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>;

  /**
   * 搜索任务
   * @param data 参数对象
   * @returns 任务列表
   */
  search(data: { keyword: string, uid: string, teamId?: number | null }): Promise<UploadTask.IUploadTask[]>;

  /**
   * 更新任务状态
   * @param data 参数对象
   * @returns 是否更新成功
   */
  updateStatus(data: { id: number, status: number, progress?: number, reason?: string }): Promise<boolean>;

  /**
   * 批量移动任务到指定文件夹
   * @param data 参数对象
   * @returns 移动的记录数
   */
  batchMove(data: { ids: number[], folderId: string }): Promise<number>;
}
