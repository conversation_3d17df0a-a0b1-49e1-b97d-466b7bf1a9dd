import { Injectable, Inject } from '@nestjs/common'
import { dialog } from 'electron'
import { statSync } from 'fs'
import { basename } from 'path'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { UploadTaskRepository } from './upload-task.repository.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'

import { createHash } from 'crypto'
import { UploadTask } from '@app/shared/types/database.types.js'
import type { UploadQueueManager } from './upload-queue-manager.js'

/**
 * 上传任务服务类
 */
@Injectable()
export class UploadTaskService extends CrudableBaseService<
  UploadTaskModel,
  UploadTask.CreateParams,
  UploadTask.UpdateParams,
  UploadTask.QueryParams,
  UploadTaskRepository
> {

  /**
   * 默认队列配置
   */
  private queueConfig: UploadTask.QueueConfig = {
    max_concurrent_uploads: 3,
    retry_attempts: 3,
    retry_delay: 5000,
    chunk_size: 1024 * 1024 // 1MB
  }

  /**
   * 队列管理器引用（延迟注入避免循环依赖）
   */
  private queueManager?: UploadQueueManager

  constructor(
    @Inject(UploadTaskRepository)
    repository: UploadTaskRepository
  ) {
    super(repository)

    // 延迟初始化，确保数据库已准备就绪
    this.scheduleInitialization()
  }

  /**
   * 设置队列管理器引用（由队列管理器调用）
   */
  setQueueManager(queueManager: UploadQueueManager): void {
    this.queueManager = queueManager
  }

  /**
   * 调度初始化，等待数据库准备就绪
   */
  private scheduleInitialization(): void {
    const checkAndInit = () => {
      try {
        // 尝试访问数据库，如果失败则继续等待
        this.repository.resetUploadingTasks()
        console.log('[UploadTaskService] 初始化成功')
      } catch (error) {
        // 数据库还未准备好，继续等待
        setTimeout(checkAndInit, 2000)
      }
    }

    // 延迟开始检查
    setTimeout(checkAndInit, 3000)
  }

  /**
   * 重置上传中的任务为等待状态
   */
  resetUploadingTasks(): number {
    return this.repository.resetUploadingTasks()
  }

  /**
   * 获取用户上传任务
   */
  getUserTasks(uid: string, status?: UploadTask.Status, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.findUserTasks(uid, status, teamId)
    } catch (error: any) {
      throw new Error(`获取用户上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取文件夹下的上传任务
   */
  getTasksByFolder(folderId: string, uid: string, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.findTasksByFolder(folderId, uid, teamId)
    } catch (error: any) {
      throw new Error(`获取文件夹上传任务失败: ${error.message}`)
    }
  }

  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: UploadTask.Status, uid?: string, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.findTasksByStatus(status, uid, teamId)
    } catch (error: any) {
      throw new Error(`根据状态获取上传任务失败: ${error.message}`)
    }
  }

  /**
   * 搜索上传任务
   */
  searchTasks(keyword: string, uid: string, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.search(keyword, uid, teamId)
    } catch (error: any) {
      throw new Error(`搜索上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取任务统计
   */
  getTaskStats(uid: string, teamId?: number | null): UploadTask.StatsResult {
    try {
      return this.repository.getTaskStats(uid, teamId)
    } catch (error: any) {
      throw new Error(`获取上传任务统计失败: ${error.message}`)
    }
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(id: number, progress: number): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.updateProgress(progress)
      return this.repository.update(id, {
        progress: task.progress
      })
    } catch (error: any) {
      throw new Error(`更新任务进度失败: ${error.message}`)
    }
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(id: number, status: UploadTask.Status, reason?: string): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.setStatus(status, reason)
      return this.repository.update(id, {
        status: task.status,
        reason: task.reason
      })
    } catch (error: any) {
      throw new Error(`更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 批量更新任务状态
   */
  batchUpdateStatus(ids: number[], status: UploadTask.Status, reason?: string): number {
    try {
      return this.repository.batchUpdateStatus(ids, status, reason)
    } catch (error: any) {
      throw new Error(`批量更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 暂停任务
   */
  async pauseTask(id: number): Promise<boolean> {
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canPause()) {
      throw new Error('任务当前状态不允许暂停')
    }

    // 调用队列管理器的暂停方法，它会处理正在上传的任务或直接更新状态
    if (this.queueManager) {
      console.log(`[UploadTaskService] 调用队列管理器暂停任务 ${id}`)
      return await this.queueManager.pauseUpload(id)
    }

    // 如果没有队列管理器，直接更新数据库状态
    return this.updateTaskStatus(id, UploadTask.Status.PAUSED)
  }

  /**
   * 恢复任务
   */
  async resumeTask(id: number): Promise<boolean> {
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canResume()) {
      throw new Error('任务当前状态不允许恢复')
    }

    // 调用队列管理器的恢复方法，它会处理暂停的任务或直接更新状态
    if (this.queueManager) {
      console.log(`[UploadTaskService] 调用队列管理器恢复任务 ${id}`)
      return await this.queueManager.resumeUpload(id)
    }

    // 如果没有队列管理器，直接更新状态为等待
    return this.updateTaskStatus(id, UploadTask.Status.PENDING)
  }

  /**
   * 取消任务
   */
  cancelTask(id: number): boolean {
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canCancel()) {
      throw new Error('任务当前状态不允许取消')
    }

    return this.updateTaskStatus(id, UploadTask.Status.CANCELLED, '用户取消')
  }

  /**
   * 重试任务
   */
  retryTask(id: number): boolean {
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canRetry()) {
      throw new Error('任务当前状态不允许重试')
    }

    task.reset()
    return this.repository.update(id, {
      progress: task.progress,
      status: task.status,
      reason: task.reason
    })
  }

  /**
   * 开始上传任务
   */
  async startTask(taskId: number): Promise<boolean> {
    try {
      const task = this.repository.findById(taskId)
      if (!task) {
        throw new Error('任务不存在')
      }

      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        throw new Error('任务当前状态不允许开始上传')
      }

      // 更新状态为等待上传，让队列管理器处理
      return this.updateTaskStatus(taskId, UploadTask.Status.PENDING)
    } catch (error: any) {
      throw new Error(`开始上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取队列配置
   */
  getQueueConfig(): UploadTask.QueueConfig {
    return { ...this.queueConfig }
  }

  /**
   * 更新队列配置
   */
  updateQueueConfig(config: Partial<UploadTask.QueueConfig>): boolean {
    try {
      this.queueConfig = { ...this.queueConfig, ...config }
      return true
    } catch (error: any) {
      throw new Error(`更新队列配置失败: ${error.message}`)
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    active_count: number
    pending_count: number
    paused_count: number
    max_concurrent: number
  } {
    try {
      const uploadingTasks = this.repository.findTasksByStatus(UploadTask.Status.UPLOADING)
      const pendingTasks = this.repository.findTasksByStatus(UploadTask.Status.PENDING)
      const pausedTasks = this.repository.findTasksByStatus(UploadTask.Status.PAUSED)

      return {
        active_count: uploadingTasks.length,
        pending_count: pendingTasks.length,
        paused_count: pausedTasks.length,
        max_concurrent: this.queueConfig.max_concurrent_uploads
      }
    } catch (error: any) {
      throw new Error(`获取队列状态失败: ${error.message}`)
    }
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(uid: string, teamId?: number | null, olderThanDays: number = 30): number {
    try {
      return this.repository.cleanupCompleted(uid, teamId, olderThanDays)
    } catch (error: any) {
      throw new Error(`清理已完成任务失败: ${error.message}`)
    }
  }

  /**
   * 上传文件内容
   */
  async uploadFileContent(
    taskId: number,
    fileContent: ArrayBuffer | Uint8Array,
    fileName: string // API兼容性参数，当前实现中未使用
  ): Promise<{ success: boolean, url?: string, error?: string }> {
    try {
      // 获取任务信息
      const task = this.repository.findById(taskId)
      if (!task) {
        return { success: false, error: '任务不存在' }
      }

      // 检查任务状态
      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        return { success: false, error: '任务当前状态不允许上传' }
      }

      // 更新任务状态为上传中
      this.updateTaskStatus(taskId, UploadTask.Status.UPLOADING)

      // 转换文件内容为 Buffer
      const buffer = fileContent instanceof ArrayBuffer
        ? Buffer.from(fileContent)
        : Buffer.from(fileContent.buffer)

      // 计算文件哈希
      const hash = createHash('md5').update(buffer).digest('hex')

      // 更新任务哈希值
      this.repository.update(taskId, { hash })

      // 将任务状态设置为等待，让队列管理器处理
      this.updateTaskStatus(taskId, UploadTask.Status.PENDING)

      console.log(`[UploadTaskService] 任务 ${taskId} 已添加到上传队列`)
      return { success: true }
    } catch (error: any) {
      console.error('[UploadTaskService] 上传文件内容失败:', error)
      this.updateTaskStatus(taskId, UploadTask.Status.FAILED, error.message || '上传异常')
      return { success: false, error: error.message || '上传异常' }
    }
  }

  /**
   * 选择文件
   */
  async selectFiles(
    multiple: boolean = true,
    filters?: Array<{ name: string, extensions: string[] }>
  ): Promise<string[]> {
    try {
      const defaultFilters = [
        { name: '视频文件', extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'] },
        { name: '音频文件', extensions: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'] },
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'] },
        { name: '所有文件', extensions: ['*'] }
      ]

      const result = await dialog.showOpenDialog({
        properties: multiple ? ['openFile', 'multiSelections'] : ['openFile'],
        filters: filters || defaultFilters 
      })

      return result.canceled ? [] : result.filePaths
    } catch (error: any) {
      console.error('[UploadTaskService] 选择文件失败:', error)
      return []
    }
  }

  /**
   * 从本地路径上传文件
   */
  async uploadFromPath(
    taskId: number,
    filePath: string
  ): Promise<{ success: boolean, url?: string, error?: string }> {
    try {
      // 获取任务信息
      const task = this.repository.findById(taskId)
      if (!task) {
        return { success: false, error: '任务不存在' }
      }

      // 检查任务状态
      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        return { success: false, error: '任务当前状态不允许上传' }
      }

      // 检查文件是否存在
      try {
        const stats = statSync(filePath)
        if (!stats.isFile()) {
          return { success: false, error: '指定路径不是文件' }
        }
      } catch {
        return { success: false, error: '文件不存在或无法访问' }
      }

      // 更新任务的本地路径
      this.repository.update(taskId, {
        local_path: filePath,
        name: basename(filePath)
      })

      // 将任务状态设置为等待，让队列管理器处理
      this.updateTaskStatus(taskId, UploadTask.Status.PENDING)

      console.log(`[UploadTaskService] 任务 ${taskId} 已添加到上传队列，文件路径: ${filePath}`)
      return { success: true }
    } catch (error: any) {
      console.error('[UploadTaskService] 从路径上传文件失败:', error)
      this.updateTaskStatus(taskId, UploadTask.Status.FAILED, error.message || '上传异常')
      return { success: false, error: error.message || '上传异常' }
    }
  }
}
