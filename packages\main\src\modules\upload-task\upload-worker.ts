import { parentPort } from 'worker_threads'
import { createReadStream, statSync, existsSync } from 'fs'
import { createHash } from 'crypto'
import { resolve } from 'path'
import OSS from 'ali-oss'

interface WorkerTaskData {
  taskId: number
  filePath: string
  fileName: string
  uploadConfig: {
    folderUuid?: string
    fileMd5?: string
    module: string
    partSize: number
    checkpointData?: string  // 断点续传数据
  }
}

interface WorkerMessage {
  type: 'upload' | 'pause' | 'resume' | 'cancel'
  taskId?: number
  data?: any
}

interface STSSignature {
  accessKeyId: string
  secretAccessKey: string
  securityToken: string
  expiration: string
  platform: number
  callback: string
  callbackVar: string
  objectKey: string
  endpoint: string
  bucket: string
  objectId: string
}

class UploadWorker {

  private activeUploads: Map<number, {
    isPaused: boolean
    ossClient?: OSS
    checkpoint?: any
    uploadPromise?: Promise<any>
  }> = new Map()

  private progressThrottlers: Map<number, {
    lastReportTime: number
    lastProgress: number
  }> = new Map()

  private readonly PROGRESS_REPORT_INTERVAL = 500 

  constructor() {
    this.setupMessageHandler()
  }

  private setupMessageHandler(): void {
    if (!parentPort) {
      throw new Error('Worker必须在worker_threads环境中运行')
    }

    parentPort.on('message', async (message: WorkerMessage) => {
      try {
        await this.handleMessage(message)
      } catch (error) {
        console.error('[UploadWorker] 处理消息时出错:', error)
        this.sendMessage('error', message.taskId || 0, {
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    })
  }

  private async handleMessage(message: WorkerMessage): Promise<void> {
    const { type, taskId, data } = message

    switch (type) {
      case 'upload':
        await this.handleUpload(data as WorkerTaskData)
        break
      case 'pause':
        this.handlePause(taskId!)
        break
      case 'resume':
        this.handleResume(taskId!)
        break
      case 'cancel':
        this.handleCancel(taskId!)
        break
      default:
        console.warn(`[UploadWorker] 未知的消息类型: ${type}`)
    }
  }

  private async handleUpload(taskData: WorkerTaskData): Promise<void> {
    const { taskId, filePath, fileName, uploadConfig } = taskData

    try {
      console.log(`[UploadWorker] 开始处理任务 ${taskId}: ${fileName}`)

      if (!this.validateFilePath(filePath)) {
        throw new Error('文件路径不安全')
      }

      if (!this.fileExists(filePath)) {
        throw new Error('文件不存在或无法访问')
      }

      const fileStats = statSync(filePath)
      const fileSize = fileStats.size

      let fileMd5 = uploadConfig.fileMd5
      if (!fileMd5) {
        fileMd5 = await this.calculateFileHash(filePath)
      }

      const signature = await this.getOSSSignature({
        fileName,
        folderUuid: uploadConfig.folderUuid,
        fileMd5,
        module: uploadConfig.module
      })

      const ossClient = this.createOSSClient(signature)
      const progressCallback = this.createProgressCallback(taskId, fileSize)

      // 保存上传状态，包括 OSS 客户端
      this.activeUploads.set(taskId, {
        isPaused: false,
        ossClient,
        checkpoint: undefined
      })

      // 检查是否有 checkpoint 数据（断点续传）
      const existingCheckpoint = uploadConfig.checkpointData ? JSON.parse(uploadConfig.checkpointData) : undefined

      // 开始上传（支持断点续传）
      const uploadOptions: any = {
        progress: progressCallback,
        partSize: uploadConfig.partSize,
      }

      // 如果有 checkpoint 数据，添加到上传选项中
      if (existingCheckpoint) {
        uploadOptions.checkpoint = existingCheckpoint
        console.log(`[UploadWorker] 任务 ${taskId} 使用断点续传`)
      }

      const uploadPromise = ossClient.multipartUpload(signature.objectKey, filePath, uploadOptions)

      // 保存上传 Promise
      const upload = this.activeUploads.get(taskId)!
      upload.uploadPromise = uploadPromise

      const result = await uploadPromise

      const url = new URL((result.res as any)?.requestUrls?.[0])
      const fileUrl = `${url.protocol}//${url.host}/${url.pathname}`

      this.sendMessage('complete', taskId, {
        url: fileUrl,
        fileName: fileName,
        objectId: signature.objectId
      })

      console.log(`[UploadWorker] 任务 ${taskId} 上传完成`)
    } catch (error) {
      const upload = this.activeUploads.get(taskId)

      const errorMessage = error instanceof Error ? error.message : String(error)
      const isUploadPaused = errorMessage.includes('UPLOAD_PAUSED') ||
                            (upload?.isPaused && errorMessage.includes('Failed to upload some parts'))

      if (isUploadPaused) {
        console.log(`[UploadWorker] 任务 ${taskId} 因暂停而中断`)
        // 不再发送暂停消息，因为在 handlePause 中已经发送了
        // 这里只是静默处理暂停导致的错误
      } else {
        console.error(`[UploadWorker] 任务 ${taskId} 上传失败:`, error)
        this.sendMessage('error', taskId, {
          error: error instanceof Error ? error.message : '上传失败'
        })
      }
    } finally {
      // 只有在非暂停状态下才清理上传状态
      const upload = this.activeUploads.get(taskId)
      if (!upload?.isPaused) {
        this.activeUploads.delete(taskId)
        this.progressThrottlers.delete(taskId)
      }
    }
  }

  private handlePause(taskId: number): void {
    const upload = this.activeUploads.get(taskId)

    if (upload) {
      console.log(`[UploadWorker] 暂停任务 ${taskId}`)

      // 设置暂停标志
      upload.isPaused = true

      // 立即发送暂停消息，不等待 progress 回调
      this.sendMessage('paused', taskId, {
        checkpoint: upload.checkpoint
      })

      console.log(`[UploadWorker] 任务 ${taskId} 暂停消息已发送`)
    } else {
      console.warn(`[UploadWorker] 任务 ${taskId} 不存在，无法暂停`)
    }
  }

  private async handleResume(taskId: number): Promise<void> {
    const upload = this.activeUploads.get(taskId)
    if (upload && upload.isPaused) {
      console.log(`[UploadWorker] 恢复任务 ${taskId}`)

      // 重置暂停标志
      upload.isPaused = false

      // 发送恢复消息，队列管理器会重新启动上传任务
      this.sendMessage('resumed', taskId, {
        checkpoint: upload.checkpoint
      })

      console.log(`[UploadWorker] 任务 ${taskId} 恢复标志已设置，等待重新启动`)
    } else {
      console.warn(`[UploadWorker] 任务 ${taskId} 不存在或未暂停，无法恢复`)
    }
  }

  private handleCancel(taskId: number): void {
    const upload = this.activeUploads.get(taskId)
    if (upload) {
      // 设置暂停标志来中断上传
      upload.isPaused = true
      this.activeUploads.delete(taskId)
      this.progressThrottlers.delete(taskId)
      console.log(`[UploadWorker] 任务 ${taskId} 已取消`)
    }
  }

  private createProgressCallback(taskId: number, fileSize: number) {
    return (progress: number, checkpoint: any) => {
      const upload = this.activeUploads.get(taskId)

      // 保存 checkpoint 数据用于断点续传
      if (upload && checkpoint) {
        upload.checkpoint = checkpoint
      }

      // 检查暂停标志，如果已暂停则抛出错误中断上传
      if (upload?.isPaused) {
        console.log(`[UploadWorker] 任务 ${taskId} 已暂停，中断上传`)
        throw new Error('UPLOAD_PAUSED')
      }

      const throttler = this.progressThrottlers.get(taskId) || {
        lastReportTime: 0,
        lastProgress: 0
      }

      const now = Date.now()
      const progressDiff = Math.abs(progress - throttler.lastProgress)

      if (now - throttler.lastReportTime >= this.PROGRESS_REPORT_INTERVAL || progressDiff >= 0.01) {
        console.log(`[UploadWorker] 发送进度更新: 任务${taskId}, 进度${(progress * 100).toFixed(1)}%`)
        this.sendMessage('progress', taskId, {
          progress,
          checkpoint,
          uploadedBytes: Math.round(progress * fileSize)
        })

        throttler.lastReportTime = now
        throttler.lastProgress = progress
        this.progressThrottlers.set(taskId, throttler)
      }
    }
  }

  private async calculateFileHash(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = createHash('md5')
      const stream = createReadStream(filePath)

      stream.on('data', data => {
        hash.update(data)
      })

      stream.on('end', () => {
        resolve(hash.digest('hex'))
      })

      stream.on('error', error => {
        reject(error)
      })
    })
  }

  private async getOSSSignature(params: {
    fileName: string
    folderUuid?: string
    fileMd5: string
    module: string
  }): Promise<STSSignature> {
    try {
      const requestData = {
        fileName: params.fileName,
        folderUuid: params.folderUuid || '',
        fileMd5: params.fileMd5,
        module: params.module
      }

      return new Promise((resolve, reject) => {
        const requestId = Date.now().toString()

        const timeout = setTimeout(() => {
          reject(new Error('获取OSS签名超时'))
        }, 30000)

        const messageHandler = (message: any) => {
          if (message.type === 'oss-signature-response' && message.requestId === requestId) {
            clearTimeout(timeout)
            if (parentPort) {
              parentPort.off('message', messageHandler)
            }

            if (message.success) {
              resolve(message.data)
            } else {
              reject(new Error(message.error || '获取OSS签名失败'))
            }
          }
        }

        if (parentPort) {
          parentPort.on('message', messageHandler)
          parentPort.postMessage({
            type: 'request-oss-signature',
            requestId,
            data: requestData
          })
        } else {
          reject(new Error('Worker未正确初始化'))
        }
      })
    } catch (error) {
      throw new Error(`获取OSS签名失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  private createOSSClient(signature: STSSignature): OSS {
    return new OSS({
      region: 'oss-cn-hangzhou',
      accessKeyId: signature.accessKeyId,
      accessKeySecret: signature.secretAccessKey,
      stsToken: signature.securityToken,
      bucket: signature.bucket,
      endpoint: signature.endpoint
    })
  }

  private sendMessage(type: string, taskId: number, data: any): void {
    if (parentPort) {
      parentPort.postMessage({
        type,
        taskId,
        data
      })
    }
  }

  private fileExists(filePath: string): boolean {
    try {
      return existsSync(filePath)
    } catch {
      return false
    }
  }

  /**
   * 验证文件路径安全性，防止路径遍历攻击
   */
  private validateFilePath(filePath: string): boolean {
    try {
      // 检查路径是否包含危险字符
      const dangerousPatterns = ['../', '..\\', '/etc/', '/root/', 'C:\\Windows\\', 'C:\\System32\\']
      if (dangerousPatterns.some(pattern => filePath.includes(pattern))) {
        console.warn(`[UploadWorker] 检测到危险路径模式: ${filePath}`)
        return false
      }

      return true
    } catch (error) {
      console.error(`[UploadWorker] 路径验证失败: ${error}`)
      return false
    }
  }
}

new UploadWorker()